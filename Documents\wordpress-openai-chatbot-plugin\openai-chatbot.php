<?php
/**
 * Plugin Name: CR OpenAI Chatbot
 * Plugin URI: https://example.com/openai-chatbot
 * Description: A production-ready WordPress plugin that integrates OpenAI's Assistants API to create an intelligent website chatbot with secure API handling, conversation management, and responsive design.
 * Version: 1.3.1
 * Author: <PERSON><PERSON>
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: openai-chatbot
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 *
 * @package OpenAI_Chatbot
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

error_log('OpenAI Chatbot: Plugin file loaded.');

// Define plugin constants
define('OPENAI_CHATBOT_VERSION', '1.3.1');
define('OPENAI_CHATBOT_PLUGIN_URL', plugin_dir_url(__FILE__));
define('OPENAI_CHATBOT_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('OPENAI_CHATBOT_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main OpenAI Chatbot Plugin Class
 * 
 * Handles plugin initialization, asset loading, and core functionality
 */
class OpenAI_Chatbot_Plugin {

    /**
     * Single instance of the plugin
     * 
     * @var OpenAI_Chatbot_Plugin
     */
    private static $instance = null;

    /**
     * OpenAI API handler instance
     * 
     * @var OpenAI_Chatbot_API
     */
    private $api_handler;

    /**
     * Get single instance of the plugin
     * 
     * @return OpenAI_Chatbot_Plugin
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor - Initialize the plugin
     */
    private function __construct() {
        error_log('OpenAI Chatbot: Constructor started.');
        $this->init_hooks();
        error_log('OpenAI Chatbot: Hooks initialized.');
        $this->load_dependencies();
        error_log('OpenAI Chatbot: Dependencies loaded.');
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Plugin activation and deactivation
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));

        // Initialize plugin after WordPress loads
        add_action('init', array($this, 'init'));
        
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));

        // Add REST API endpoints
        add_action('rest_api_init', array($this, 'register_rest_routes'));

        // Add chat widget to footer
        add_action('wp_footer', array($this, 'render_chat_widget'));

        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));

        // Add shortcode support
        add_shortcode('openai_chatbot', array($this, 'chatbot_shortcode'));

        // AJAX handlers for logged-in and non-logged-in users
        add_action('wp_ajax_openai_chat', array($this, 'handle_chat_request'));
        add_action('wp_ajax_nopriv_openai_chat', array($this, 'handle_chat_request'));
    }

    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        require_once OPENAI_CHATBOT_PLUGIN_PATH . 'includes/class-openai-api.php';
        require_once OPENAI_CHATBOT_PLUGIN_PATH . 'includes/class-rate-limiter.php';
        require_once OPENAI_CHATBOT_PLUGIN_PATH . 'includes/class-security-handler.php';

        // Initialize API handler with error handling
        $this->init_api_handler();
    }

    /**
     * Initialize API handler with proper error handling
     */
    private function init_api_handler() {
        try {
            $this->api_handler = new OpenAI_Chatbot_API();
        } catch (Exception $e) {
            // Log the error but don't break the plugin
            error_log('OpenAI Chatbot: API initialization failed - ' . $e->getMessage());
            $this->api_handler = null;

            // Add admin notice if in admin area
            if (is_admin()) {
                add_action('admin_notices', function() use ($e) {
                    echo '<div class="notice notice-error"><p>';
                    echo __('OpenAI Chatbot: ', 'openai-chatbot') . esc_html($e->getMessage());
                    echo ' <a href="' . admin_url('options-general.php?page=openai-chatbot') . '">';
                    echo __('Configure Settings', 'openai-chatbot');
                    echo '</a></p></div>';
                });
            }
        }
    }

    /**
     * Get API handler instance, initializing if needed
     */
    private function get_api_handler() {
        if ($this->api_handler === null) {
            $this->init_api_handler();
        }
        return $this->api_handler;
    }

    /**
     * Plugin activation hook
     */
    public function activate() {
        // Start logging
        error_log('OpenAI Chatbot: Activation process started.');

        // Check for required PHP version
        if (version_compare(PHP_VERSION, '7.4', '<')) {
            error_log('OpenAI Chatbot: PHP version check failed. Requires 7.4, but found ' . PHP_VERSION);
            deactivate_plugins(OPENAI_CHATBOT_PLUGIN_BASENAME);
            wp_die(__('OpenAI Chatbot requires PHP 7.4 or higher.', 'openai-chatbot'));
        }
        error_log('OpenAI Chatbot: PHP version check passed.');

        // Check for OpenAI API key (database or wp-config)
        $api_key = get_option('openai_chatbot_api_key', '');
        if (empty($api_key) && (!defined('OPENAI_API_KEY') || empty(OPENAI_API_KEY))) {
            error_log('OpenAI Chatbot: OpenAI API key is missing.');
            add_action('admin_notices', array($this, 'api_key_missing_notice'));
        } else {
            error_log('OpenAI Chatbot: OpenAI API key found.');
        }

        // Create database tables if needed
        error_log('OpenAI Chatbot: Attempting to create database tables...');
        $this->create_database_tables();
        error_log('OpenAI Chatbot: Database table creation process finished.');

        // Set default options
        error_log('OpenAI Chatbot: Setting default options...');
        $this->set_default_options();
        error_log('OpenAI Chatbot: Default options set.');

        // Flush rewrite rules
        error_log('OpenAI Chatbot: Flushing rewrite rules...');
        flush_rewrite_rules();
        error_log('OpenAI Chatbot: Rewrite rules flushed.');

        error_log('OpenAI Chatbot: Activation process completed successfully.');
    }

    /**
     * Plugin deactivation hook
     */
    public function deactivate() {
        // Clean up transients
        $this->cleanup_transients();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Initialize plugin
     */
    public function init() {
        // Load text domain for translations
        load_plugin_textdomain('openai-chatbot', false, dirname(OPENAI_CHATBOT_PLUGIN_BASENAME) . '/languages');
    }

    /**
     * Enqueue frontend assets (CSS and JavaScript)
     */
    public function enqueue_frontend_assets() {
        // Only load on pages where chatbot should appear
        if (!$this->should_load_chatbot()) {
            return;
        }

        // Enqueue CSS
        wp_enqueue_style(
            'openai-chatbot-style',
            OPENAI_CHATBOT_PLUGIN_URL . 'assets/css/chatbot.css',
            array(),
            OPENAI_CHATBOT_VERSION
        );

        // Add custom colors CSS
        $this->add_custom_colors_css();

        // Enqueue JavaScript with cache busting for debugging
        wp_enqueue_script(
            'openai-chatbot-script',
            OPENAI_CHATBOT_PLUGIN_URL . 'assets/js/chatbot.js',
            array('jquery'),
            OPENAI_CHATBOT_VERSION . '.' . time(), // Add timestamp for cache busting during debugging
            true
        );

        // Localize script with AJAX URL and nonce
        wp_localize_script('openai-chatbot-script', 'openai_chatbot_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'rest_url' => rest_url('openai-chatbot/v1/'),
            'nonce' => wp_create_nonce('wp_rest'),
            'strings' => array(
                'placeholder' => __('Type your message...', 'openai-chatbot'),
                'send' => __('Send', 'openai-chatbot'),
                'error' => __('Sorry, something went wrong. Please try again.', 'openai-chatbot'),
                'rate_limit' => __('Please wait before sending another message.', 'openai-chatbot'),
                'connecting' => __('Connecting...', 'openai-chatbot'),
                'thinking' => __('AI is thinking...', 'openai-chatbot'),
                'permission_denied' => __('Permission denied. Please refresh the page and try again.', 'openai-chatbot')
            ),
            'settings' => array(
                'max_message_length' => 1000,
                'typing_delay' => 50,
                'auto_scroll' => true,
                'show_timestamps' => get_option('openai_chatbot_show_timestamps', false)
            )
        ));
    }

    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only load on plugin settings page
        if ('settings_page_openai-chatbot' !== $hook) {
            return;
        }

        // Enqueue admin CSS
        wp_enqueue_style(
            'openai-chatbot-admin-style',
            OPENAI_CHATBOT_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            OPENAI_CHATBOT_VERSION
        );

        // Enqueue WordPress color picker
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');

        // Enqueue admin JavaScript
        wp_enqueue_script(
            'openai-chatbot-admin-script',
            OPENAI_CHATBOT_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery', 'wp-color-picker'),
            OPENAI_CHATBOT_VERSION,
            true
        );
    }

    /**
     * Register REST API routes
     */
    public function register_rest_routes() {
        register_rest_route('openai-chatbot/v1', '/chat', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_rest_chat_request'),
            'permission_callback' => array($this, 'check_chat_permissions'),
            'args' => array(
                'message' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_textarea_field',
                    'validate_callback' => array($this, 'validate_message')
                ),
                'conversation_id' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field'
                )
            )
        ));
    }

    /**
     * Add custom colors CSS to the page
     */
    private function add_custom_colors_css() {
        $bot_text_color = get_option('openai_chatbot_bot_text_color', '#1e1e1e');
        $user_text_color = get_option('openai_chatbot_user_text_color', '#ffffff');

        // Validate colors (ensure they're valid hex colors)
        if (!preg_match('/^#[a-f0-9]{6}$/i', $bot_text_color)) {
            $bot_text_color = '#1e1e1e';
        }
        if (!preg_match('/^#[a-f0-9]{6}$/i', $user_text_color)) {
            $user_text_color = '#ffffff';
        }

        $custom_css = "
        :root {
            --chatbot-bot-text-color: {$bot_text_color} !important;
            --chatbot-user-text-color: {$user_text_color} !important;
        }

        /* Ensure colors are applied with higher specificity */
        .openai-chatbot .bot-message .message-content {
            color: {$bot_text_color} !important;
        }

        .openai-chatbot .user-message .message-content {
            color: {$user_text_color} !important;
        }
        ";

        wp_add_inline_style('openai-chatbot-style', $custom_css);
    }

    /**
     * Handle REST API chat request
     */
    public function handle_rest_chat_request($request) {
        error_log('OpenAI Chatbot: REST chat request received');
        
        // Verify nonce for security
        $nonce = $request->get_header('X-WP-Nonce');
        error_log('OpenAI Chatbot: Nonce received: ' . ($nonce ? 'Yes' : 'No'));
        
        if (!wp_verify_nonce($nonce, 'wp_rest')) {
            error_log('OpenAI Chatbot: Nonce verification failed');
            return new WP_Error('invalid_nonce', __('Invalid security token.', 'openai-chatbot'), array('status' => 403));
        }
        error_log('OpenAI Chatbot: Nonce verification passed');

        // Rate limiting check - TEMPORARILY DISABLED FOR TESTING
        error_log('OpenAI Chatbot: Rate limiting temporarily disabled for testing');
        /*
        error_log('OpenAI Chatbot: Checking rate limits');
        $rate_limiter = new OpenAI_Chatbot_Rate_Limiter();
        if (!$rate_limiter->check_rate_limit()) {
            error_log('OpenAI Chatbot: Rate limit exceeded');
            return new WP_Error('rate_limit_exceeded', __('Rate limit exceeded. Please wait before sending another message.', 'openai-chatbot'), array('status' => 429));
        }
        error_log('OpenAI Chatbot: Rate limit check passed');
        */

        // Get and validate parameters
        $message = $request->get_param('message');
        $conversation_id = $request->get_param('conversation_id');
        error_log('OpenAI Chatbot: Message received: ' . substr($message, 0, 50) . '...');

        try {
            error_log('OpenAI Chatbot: Attempting to send message to OpenAI API');

            // Get API handler and ensure it's properly initialized
            $api_handler = $this->get_api_handler();
            if ($api_handler === null) {
                throw new Exception(__('API handler not properly configured.', 'openai-chatbot'));
            }

            // Send message to OpenAI API
            $response = $api_handler->send_message($message, $conversation_id);
            
            // Log the full response for debugging
            error_log('OpenAI Chatbot: Full API response: ' . json_encode($response));
            
            // Validate response structure
            if (!is_array($response) || !isset($response['message'])) {
                error_log('OpenAI Chatbot: Invalid response structure from API');
                return new WP_Error('invalid_response', __('Invalid response from AI service.', 'openai-chatbot'), array('status' => 500));
            }
            
            // Log successful request
            error_log('OpenAI Chatbot: Successful API request - Message: ' . substr($message, 0, 50) . '...');

            return rest_ensure_response(array(
                'success' => true,
                'message' => $response['message'],
                'conversation_id' => $response['conversation_id'] ?? null,
                'timestamp' => current_time('timestamp')
            ));

        } catch (Exception $e) {
            // Log error
            error_log('OpenAI Chatbot Error: ' . $e->getMessage());
            
            return new WP_Error('api_error', __('Unable to process your request. Please try again later.', 'openai-chatbot'), array('status' => 500));
        }
    }

    /**
     * Check permissions for chat requests
     */
    public function check_chat_permissions($request) {
        // Allow all users to use chat (can be customized based on requirements)
        return true;
    }

    /**
     * Validate chat message
     */
    public function validate_message($value, $request, $param) {
        if (empty(trim($value))) {
            return new WP_Error('empty_message', __('Message cannot be empty.', 'openai-chatbot'));
        }

        if (strlen($value) > 1000) {
            return new WP_Error('message_too_long', __('Message is too long. Maximum 1000 characters allowed.', 'openai-chatbot'));
        }

        return true;
    }

    /**
     * Legacy AJAX handler for backward compatibility
     */
    public function handle_chat_request() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'openai_chatbot_nonce')) {
            wp_die(__('Security check failed.', 'openai-chatbot'));
        }

        // Create WP_REST_Request object and delegate to REST handler
        $request = new WP_REST_Request('POST', '/openai-chatbot/v1/chat');
        $request->set_param('message', sanitize_textarea_field($_POST['message']));
        $request->set_param('conversation_id', sanitize_text_field($_POST['conversation_id'] ?? ''));

        $response = $this->handle_rest_chat_request($request);
        
        if (is_wp_error($response)) {
            wp_send_json_error($response->get_error_message());
        } else {
            wp_send_json_success($response->data);
        }
    }

    /**
     * Render chat widget HTML
     */
    public function render_chat_widget() {
        if (!$this->should_load_chatbot()) {
            return;
        }

        include OPENAI_CHATBOT_PLUGIN_PATH . 'templates/chat-widget.php';
    }

    /**
     * Chatbot shortcode handler
     */
    public function chatbot_shortcode($atts) {
        $atts = shortcode_atts(array(
            'position' => 'inline',
            'height' => '400px',
            'width' => '100%'
        ), $atts, 'openai_chatbot');

        ob_start();
        include OPENAI_CHATBOT_PLUGIN_PATH . 'templates/chat-shortcode.php';
        return ob_get_clean();
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_options_page(
            __('OpenAI Chatbot Settings', 'openai-chatbot'),
            __('OpenAI Chatbot', 'openai-chatbot'),
            'manage_options',
            'openai-chatbot',
            array($this, 'admin_page')
        );
    }

    /**
     * Admin page callback
     */
    public function admin_page() {
        include OPENAI_CHATBOT_PLUGIN_PATH . 'admin/settings-page.php';
    }

    /**
     * Check if chatbot should load on current page
     */
    private function should_load_chatbot() {
        // Don't load in admin area
        if (is_admin()) {
            return false;
        }

        // Check if disabled on current page type
        $disabled_on = get_option('openai_chatbot_disabled_on', array());
        
        if (is_home() && in_array('home', $disabled_on)) {
            return false;
        }
        
        if (is_single() && in_array('posts', $disabled_on)) {
            return false;
        }
        
        if (is_page() && in_array('pages', $disabled_on)) {
            return false;
        }

        return true;
    }

    /**
     * Create database tables
     */
    private function create_database_tables() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'openai_chatbot_conversations';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            conversation_id varchar(255) NOT NULL,
            user_id bigint(20) DEFAULT NULL,
            user_ip varchar(45) NOT NULL,
            message_count int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY conversation_id (conversation_id),
            KEY user_id (user_id),
            KEY created_at (created_at)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $defaults = array(
            // API Configuration
            'openai_chatbot_api_key' => '',
            'openai_chatbot_assistant_id' => '',
            'openai_chatbot_system_instructions' => sprintf(
                __('You are a helpful assistant for %s. Be concise, friendly, and helpful. If you don\'t know something, say so honestly.', 'openai-chatbot'),
                get_bloginfo('name')
            ),
            'openai_chatbot_model_name' => 'gpt-4o',
            'openai_chatbot_tools_enabled' => array('file_search', 'code_interpreter'),
            'openai_chatbot_custom_tools' => '',

            // General Settings
            'openai_chatbot_enabled' => true,
            'openai_chatbot_position' => 'bottom-right',
            'openai_chatbot_theme' => 'default',
            'openai_chatbot_welcome_message' => __('Hello! How can I help you today?', 'openai-chatbot'),
            'openai_chatbot_placeholder' => __('Type your message...', 'openai-chatbot'),
            'openai_chatbot_show_timestamps' => false,
            'openai_chatbot_max_messages' => 50,
            'openai_chatbot_rate_limit' => 10,
            'openai_chatbot_disabled_on' => array(),
            'openai_chatbot_bot_text_color' => '#1e1e1e',
            'openai_chatbot_user_text_color' => '#ffffff'
        );

        foreach ($defaults as $option => $value) {
            if (get_option($option) === false) {
                add_option($option, $value);
            }
        }
    }

    /**
     * Clean up transients on deactivation
     */
    private function cleanup_transients() {
        global $wpdb;
        
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_openai_chatbot_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_openai_chatbot_%'");
    }

    /**
     * Display API key missing notice
     */
    public function api_key_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p>
                <?php _e('OpenAI Chatbot: Please configure your OpenAI API key.', 'openai-chatbot'); ?>
                <a href="<?php echo admin_url('options-general.php?page=openai-chatbot'); ?>" class="button button-primary" style="margin-left: 10px;">
                    <?php _e('Configure Settings', 'openai-chatbot'); ?>
                </a>
            </p>
            <p>
                <?php _e('Alternatively, you can add it to wp-config.php:', 'openai-chatbot'); ?>
                <code>define('OPENAI_API_KEY', 'your-api-key-here');</code>
            </p>
        </div>
        <?php
    }
}

// Initialize the plugin
OpenAI_Chatbot_Plugin::get_instance();